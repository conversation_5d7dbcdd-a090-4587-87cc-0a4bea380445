"""
Optimized ML inference service for text classification.

This module provides a high-performance text classification service that processes
treatment descriptions and predicts categories using a trained Keras model with
comprehensive preprocessing pipeline.
"""

import json
import os
import pickle
import re
import time
from dataclasses import dataclass
from importlib.resources import files
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import nltk
import numpy as np
import pandas as pd
import tensorflow as tf
from dotenv import load_dotenv
from log_utils import setup_logging
from loguru import logger
from nltk import PorterStemmer
from nltk.corpus import stopwords
from symspellpy import SymSpell, Verbosity
from tensorflow import keras

# Import Azure ML integration
try:
    from azure_ml_client import get_azure_ml_model_and_components, get_azure_ml_model_path, is_azure_ml_enabled

    AZURE_ML_AVAILABLE = True
except ImportError as e:
    logger.warning(f'⚠️ Azure ML integration not available: {str(e)}')
    AZURE_ML_AVAILABLE = False

# Load environment variables
load_dotenv()

# Configure TensorFlow to use CPU only and suppress CUDA warnings
# This fixes the "Could not load dynamic library 'libcudart.so.11.0'" error
# by forcing TensorFlow to use CPU-only execution mode
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'  # Disable GPU visibility
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suppress all TensorFlow messages except errors


# Additional TensorFlow configuration to ensure CPU-only execution
tf.config.set_visible_devices([], 'GPU')


# Initialize logging
setup_logging()


def _get_symspell_dictionary_path() -> str:
    """
    Get the path to the SymSpell dictionary file using modern importlib.resources.

    Returns:
        str: Path to the frequency_dictionary_en_82_765.txt file

    Raises:
        FileNotFoundError: If the dictionary file cannot be found
    """
    try:
        # Use modern importlib.resources approach
        symspell_files = files('symspellpy')
        dictionary_file = symspell_files / 'frequency_dictionary_en_82_765.txt'

        # Convert to string path - this works for both Python 3.9+ and older versions
        return str(dictionary_file)
    except Exception as e:
        logger.warning(f'Failed to use importlib.resources: {e}. Falling back to pkg_resources.')

    # Fallback to pkg_resources if importlib.resources is not available
    try:
        import pkg_resources

        return pkg_resources.resource_filename('symspellpy', 'frequency_dictionary_en_82_765.txt')
    except ImportError:
        raise FileNotFoundError(
            'Cannot locate SymSpell dictionary file. Please ensure symspellpy is properly installed.'
        )


@dataclass
class Config:
    """Configuration management for the scoring service."""

    # Local paths for resources and models
    # All files are now co-located in the resources/ directory at repository root
    # Use paths relative to the repository root to work from any working directory
    UTILS_PATH: str = str(Path(__file__).parent.parent / 'resources')
    MODEL_PATH: str = str(Path(__file__).parent.parent / 'resources' / 'autolodge_20250605.h5')

    # Model Configuration - configurable via environment variables
    BATCH_SIZE: int = 8
    TOP_K_PREDICTIONS: int = 5
    API_VERSION: int = 7

    # Spell Checker Configuration - configurable via environment variables
    MAX_EDIT_DISTANCE: int = 3
    MIN_WORD_LENGTH_FOR_SPELL_CHECK: int = 10

    # Input Data Logging Configuration - configurable via environment variables
    LOG_INPUT_LEVEL: str = 'METADATA_ONLY'  # Options: FULL, SAMPLE, METADATA_ONLY

    # Required utility files
    REQUIRED_UTILS: Optional[List[str]] = None

    def __post_init__(self):
        """Initialize computed fields and load environment variable overrides."""
        if self.REQUIRED_UTILS is None:
            self.REQUIRED_UTILS = ['le.pkl', 'tk.pkl', 'abbr.csv', 'corpus.pkl']

        # Load configuration from environment variables with logging
        # Disable it for now by Phil 2025/06/10 16:00:44
        self._load_from_environment()

    def _load_from_environment(self) -> None:
        """Load configuration overrides from environment variables with comprehensive logging."""
        logger.debug('🔧 Loading configuration from environment variables...')

        # Model Configuration
        batch_size_env = os.getenv('AUTOLODGE_BATCH_SIZE')
        if batch_size_env:
            try:
                self.BATCH_SIZE = int(batch_size_env)
                logger.info(f'🔧 Loaded BATCH_SIZE from environment: {self.BATCH_SIZE}')
            except ValueError:
                logger.warning(
                    f'⚠️ Invalid AUTOLODGE_BATCH_SIZE value: {batch_size_env}, using default: {self.BATCH_SIZE}'
                )
        else:
            logger.debug(f'🔧 Using default BATCH_SIZE: {self.BATCH_SIZE}')

        top_k_env = os.getenv('AUTOLODGE_TOP_K_PREDICTIONS')
        if top_k_env:
            try:
                self.TOP_K_PREDICTIONS = int(top_k_env)
                logger.info(f'🔧 Loaded TOP_K_PREDICTIONS from environment: {self.TOP_K_PREDICTIONS}')
            except ValueError:
                logger.warning(
                    f'⚠️ Invalid AUTOLODGE_TOP_K_PREDICTIONS value: {top_k_env}, using default: {self.TOP_K_PREDICTIONS}'
                )
        else:
            logger.debug(f'🔧 Using default TOP_K_PREDICTIONS: {self.TOP_K_PREDICTIONS}')

        api_version_env = os.getenv('AUTOLODGE_API_VERSION')
        if api_version_env:
            try:
                self.API_VERSION = int(api_version_env)
                logger.info(f'🔧 Loaded API_VERSION from environment: {self.API_VERSION}')
            except ValueError:
                logger.warning(
                    f'⚠️ Invalid AUTOLODGE_API_VERSION value: {api_version_env}, using default: {self.API_VERSION}'
                )
        else:
            logger.debug(f'🔧 Using default API_VERSION: {self.API_VERSION}')

        # Spell Checker Configuration
        max_edit_distance_env = os.getenv('AUTOLODGE_MAX_EDIT_DISTANCE')
        if max_edit_distance_env:
            try:
                self.MAX_EDIT_DISTANCE = int(max_edit_distance_env)
                logger.info(f'🔧 Loaded MAX_EDIT_DISTANCE from environment: {self.MAX_EDIT_DISTANCE}')
            except ValueError:
                logger.warning(
                    f'⚠️ Invalid AUTOLODGE_MAX_EDIT_DISTANCE value: {max_edit_distance_env}, using default: {self.MAX_EDIT_DISTANCE}'
                )
        else:
            logger.debug(f'🔧 Using default MAX_EDIT_DISTANCE: {self.MAX_EDIT_DISTANCE}')

        min_word_length_env = os.getenv('AUTOLODGE_MIN_WORD_LENGTH_FOR_SPELL_CHECK')
        if min_word_length_env:
            try:
                self.MIN_WORD_LENGTH_FOR_SPELL_CHECK = int(min_word_length_env)
                logger.info(
                    f'🔧 Loaded MIN_WORD_LENGTH_FOR_SPELL_CHECK from environment: {self.MIN_WORD_LENGTH_FOR_SPELL_CHECK}'
                )
            except ValueError:
                logger.warning(
                    f'⚠️ Invalid AUTOLODGE_MIN_WORD_LENGTH_FOR_SPELL_CHECK value: {min_word_length_env}, using default: {self.MIN_WORD_LENGTH_FOR_SPELL_CHECK}'
                )
        else:
            logger.debug(f'🔧 Using default MIN_WORD_LENGTH_FOR_SPELL_CHECK: {self.MIN_WORD_LENGTH_FOR_SPELL_CHECK}')

        # Input Data Logging Configuration
        log_input_level_env = os.getenv('AUTOLODGE_LOG_INPUT_LEVEL')
        if log_input_level_env:
            valid_levels = ['FULL', 'SAMPLE', 'METADATA_ONLY']
            if log_input_level_env.upper() in valid_levels:
                self.LOG_INPUT_LEVEL = log_input_level_env.upper()
                logger.info(f'🔧 Loaded LOG_INPUT_LEVEL from environment: {self.LOG_INPUT_LEVEL}')
            else:
                logger.warning(
                    f'⚠️ Invalid AUTOLODGE_LOG_INPUT_LEVEL value: {log_input_level_env}, using default: {self.LOG_INPUT_LEVEL}'
                )
                logger.info(f'💡 Valid options are: {", ".join(valid_levels)}')
        else:
            logger.debug(f'🔧 Using default LOG_INPUT_LEVEL: {self.LOG_INPUT_LEVEL}')

        logger.debug('✅ Configuration loading from environment variables completed')


class TextPreprocessor:
    """Optimized text preprocessing pipeline for treatment descriptions."""

    def __init__(self, config: Config):
        self.config = config
        self._compiled_patterns: Dict[str, Any] = {}
        self._abbreviation_table: Optional[pd.DataFrame] = None
        self._stopwords_to_remove: set = set()
        self._initialize_patterns()
        self._initialize_stopwords()

    def _initialize_patterns(self) -> None:
        """Initialize and cache compiled regex patterns for better performance."""
        self._compiled_patterns = {
            # Customized RE rules
            'cons_pattern': re.compile(r'(?<=\b)Cons(?=[A-Z]+)'),  # ConsFS, ConsCCRpt
            'rpt_pattern': re.compile(r'(?:(?<=\b)|(?<=[A-Z]))[r,R]pt'),  # Rpt
            'ordinal_pattern': re.compile(r'1st|2nd|3rd', flags=re.I),  # 1st|2nd|3rd
            'size_pattern': re.compile(r'[x|X]+([s|S]mall|[l|L]arge)'),  # xSmall, xxLarge
            'z_pattern': re.compile(r'z+(?=[A-Z])'),  # zComfortiz
            'slash_pattern': re.compile(r'(?<=\b)([A-Z])\/([A-Z])(?=\b)'),  # I/D
            # Punctuation and tokenization patterns
            'punctuation_pattern': re.compile(r'[!"#$%&\'()*+\-,/:;<=>?@[\\\]^_`{|}~\n]+'),
            'whitespace_pattern': re.compile(r'\s+'),
            'token_pattern': re.compile(
                r'(?<=\b)[a-z]\.[a-z](?:\.(?:[a-z]|\d))*\.?(?=\s|$)|(?<=\b)[a-z]{1,2}\d{1,2}[a-z]?(?=\s|$)|[a-z]+'
            ),
        }

    def _initialize_stopwords(self) -> None:
        """Initialize comprehensive stopwords list for removal."""
        self._stopwords_to_remove = set()

        # Single letters
        self._stopwords_to_remove.update(list('abcdefghijklmnopqrstuvwxyz'))

        # Units and measurements
        self._stopwords_to_remove.update([
            'kg',
            'mg',
            'gm',
            'mgmg',
            'ml',
            'litre',
            'cm',
            'km',
            'gram',
            'meter',
            'pm',
            'am',
            'mm',
            'mcg',
            'mgc',
        ])

        # Months
        self._stopwords_to_remove.update([
            'jan',
            'feb',
            'mar',
            'apr',
            'may',
            'jun',
            'jul',
            'aug',
            'sep',
            'oct',
            'nov',
            'dec',
        ])

        # Numbers as words
        self._stopwords_to_remove.update([
            'one',
            'two',
            'three',
            'four',
            'five',
            'six',
            'seven',
            'eight',
            'nine',
            'ten',
        ])

        # Days of week
        self._stopwords_to_remove.update([
            'monday',
            'tuesday',
            'wednesday',
            'thursday',
            'friday',
            'mon',
            'tue',
            'wed',
            'thu',
            'fri',
        ])

        # Other common words
        self._stopwords_to_remove.update(['per', 'yr', 'another'])

    def _load_abbreviation_table(self, utils_path: str) -> pd.DataFrame:
        """Load and cache abbreviation table for efficient lookups."""
        if self._abbreviation_table is None:
            abbr_path = os.path.join(utils_path, 'abbr.csv')
            self._abbreviation_table = pd.read_csv(abbr_path, sep='|', header=0)
            # Filter out single capital letters
            self._abbreviation_table = self._abbreviation_table[
                ~self._abbreviation_table.Abbreviation.str.contains('^[A-Z]$')
            ]
        return self._abbreviation_table

    def _apply_regex_rules(self, text: str) -> str:
        """Apply regex transformation rules to text."""
        # Apply all regex patterns
        text = self._compiled_patterns['cons_pattern'].sub('consultation ', text)
        text = self._compiled_patterns['rpt_pattern'].sub('repeat ', text)
        text = self._compiled_patterns['ordinal_pattern'].sub('', text)
        text = self._compiled_patterns['size_pattern'].sub(r'\1', text)
        text = self._compiled_patterns['z_pattern'].sub('', text)
        text = self._compiled_patterns['slash_pattern'].sub(r'\1.\2', text)
        return text

    def _expand_abbreviations(self, text: str, utils_path: str) -> str:
        """Expand abbreviations using cached lookup table."""
        abbr_table = self._load_abbreviation_table(utils_path)

        # Case-sensitive abbreviations
        cs_abbr_list = abbr_table[['Abbreviation', 'FullText']][abbr_table.Comment == 'CS']
        for _, row in cs_abbr_list.iterrows():
            old_abbr = f' {row["Abbreviation"]} '
            new_full = f' {row["FullText"]} '
            text = text.replace(old_abbr, new_full)

        # Case-insensitive abbreviations
        lc_abbr_list = abbr_table[['Abbreviation', 'FullText']][abbr_table.Comment.isna()].copy()
        lc_abbr_list['Abbreviation'] = lc_abbr_list['Abbreviation'].str.lower()

        text_lower = text.lower()
        for _, row in lc_abbr_list.iterrows():
            old_abbr = f' {row["Abbreviation"]} '
            new_full = f' {row["FullText"]} '
            text_lower = text_lower.replace(old_abbr, new_full)

        return text_lower

    def _remove_punctuation(self, text: str) -> str:
        """Remove punctuation and normalize whitespace."""
        text = self._compiled_patterns['punctuation_pattern'].sub(' ', text)
        text = self._compiled_patterns['whitespace_pattern'].sub(' ', text)
        return text.strip()

    def _tokenize_text(self, text: str) -> List[str]:
        """Extract tokens using optimized regex pattern."""
        return self._compiled_patterns['token_pattern'].findall(text)

    def _filter_tokens(self, tokens: List[str], stopwords_set: set) -> List[str]:
        """Filter out stopwords and unwanted tokens."""
        # Remove NLTK stopwords
        tokens = [word for word in tokens if word not in stopwords_set]

        # Remove additional stopwords
        tokens = [word for word in tokens if word not in self._stopwords_to_remove]

        return tokens

    def _apply_spell_correction(self, tokens: List[str], spell_checker: SymSpell, corpus: list) -> List[str]:
        """Apply spell correction to long tokens."""
        corrected_tokens = []
        for word in tokens:
            if len(word) >= self.config.MIN_WORD_LENGTH_FOR_SPELL_CHECK and spell_checker is not None:
                suggestions = spell_checker.lookup(word, Verbosity.TOP, max_edit_distance=1, include_unknown=True)
                if suggestions and suggestions[0].distance == 1 and suggestions[0].term in corpus:
                    corrected_tokens.append(suggestions[0].term)
                else:
                    corrected_tokens.append(word)
            else:
                corrected_tokens.append(word)
        return corrected_tokens

    def _apply_stemming(self, tokens: List[str]) -> List[str]:
        """Apply Porter stemming to tokens."""
        stemmer = PorterStemmer()
        return [stemmer.stem(word) for word in tokens]

    def process_text(
        self, text: str, utils_path: str, stopwords_set: set, spell_checker: SymSpell, corpus: list
    ) -> List[str]:
        """
        Process a single text through the complete preprocessing pipeline.

        Args:
            text: Input text to process
            utils_path: Path to utility files
            stopwords_set: Set of NLTK stopwords
            spell_checker: SymSpell instance for spell correction
            corpus: Corpus for spell checking validation

        Returns:
            List of processed tokens
        """
        # 1. Apply regex rules
        text = self._apply_regex_rules(text)

        # 2. Expand abbreviations
        text = self._expand_abbreviations(text, utils_path)

        # 3. Remove punctuation
        text = self._remove_punctuation(text)

        # 4. Tokenize
        tokens = self._tokenize_text(text)

        # 5. Filter stopwords
        tokens = self._filter_tokens(tokens, stopwords_set)

        # 6. Apply spell correction
        tokens = self._apply_spell_correction(tokens, spell_checker, corpus)

        # 7. Apply stemming
        tokens = self._apply_stemming(tokens)

        return tokens


class ModelResourceManager:
    """
    Manages ML model and preprocessing resources with proper lifecycle management.

    All resources (model file and utility files) are loaded from the
    resources/ directory at repository root, providing a self-contained deployment structure.
    """

    def __init__(self, config: Config):
        self.config = config
        self._model = None
        self._tokenizer = None
        self._label_encoder = None
        self._spell_checker = None
        self._stopwords = None
        self._corpus = None
        self._initialized = False

    @property
    def model(self) -> Any:
        """Get the loaded Keras model."""
        if self._model is None:
            raise RuntimeError('Model not initialized. Call initialize() first.')
        return self._model

    @property
    def tokenizer(self) -> Any:
        """Get the loaded tokenizer."""
        if self._tokenizer is None:
            raise RuntimeError('Tokenizer not initialized. Call initialize() first.')
        return self._tokenizer

    @property
    def label_encoder(self) -> Any:
        """Get the loaded label encoder."""
        if self._label_encoder is None:
            raise RuntimeError('Label encoder not initialized. Call initialize() first.')
        return self._label_encoder

    @property
    def spell_checker(self) -> SymSpell:
        """Get the loaded spell checker."""
        if self._spell_checker is None:
            raise RuntimeError('Spell checker not initialized. Call initialize() first.')
        return self._spell_checker

    @property
    def stopwords(self) -> set:
        """Get the loaded stopwords."""
        if self._stopwords is None:
            raise RuntimeError('Stopwords not initialized. Call initialize() first.')
        return self._stopwords

    @property
    def corpus(self) -> list:
        """Get the loaded corpus."""
        if self._corpus is None:
            raise RuntimeError('Corpus not initialized. Call initialize() first.')
        return self._corpus

    @property
    def is_initialized(self) -> bool:
        """Check if all resources are initialized."""
        return self._initialized

    def _load_model_from_path(self, model_path: str) -> Any:
        """
        Load Keras model from the local file path.

        Args:
            model_path: Path to the model directory or file (relative to Python/ directory)

        Returns:
            keras.Model: Loaded Keras model

        Raises:
            Exception: If model loading fails
        """
        try:
            # Handle different model path structures
            if os.path.isdir(model_path):
                # Look for common model file names in the directory
                model_files = []
                for ext in ['.h5', '.keras', '.pb']:
                    model_files.extend([f for f in os.listdir(model_path) if f.endswith(ext)])

                if model_files:
                    model_file_path = os.path.join(model_path, model_files[0])
                else:
                    # Try loading the directory as a SavedModel
                    model_file_path = model_path
            else:
                model_file_path = model_path

            logger.info(f'Loading Keras model from: {model_file_path}')
            model = keras.models.load_model(model_file_path)
            logger.info('Keras model loaded successfully')
            return model

        except Exception as e:
            logger.error(f'Failed to load Keras model: {str(e)}')
            raise

    def _load_preprocessing_components(self) -> Tuple[Any, Any, set, list, SymSpell]:
        """
        Load all preprocessing components from local resources directory.

        Loads tokenizer, label encoder, stopwords, corpus, and spell checker
        from the resources/ directory at repository root.

        Returns:
            Tuple containing tokenizer, label encoder, stopwords, corpus, and spell checker

        Raises:
            Exception: If loading any component fails
        """
        try:
            # Load tokenizer
            logger.info('Loading tokenizer...')
            with open(os.path.join(self.config.UTILS_PATH, 'tk.pkl'), 'rb') as pkl_file:
                tokenizer = pickle.load(pkl_file)

            # Load label encoder
            logger.info('Loading label encoder...')
            with open(os.path.join(self.config.UTILS_PATH, 'le.pkl'), 'rb') as pkl_file:
                label_encoder = pickle.load(pkl_file)

            # Load stopwords
            logger.info('Loading stopwords...')
            stopwords_set = set(stopwords.words('english'))

            # Load corpus
            logger.info('Loading corpus...')
            with open(os.path.join(self.config.UTILS_PATH, 'corpus.pkl'), 'rb') as pkl_file:
                corpus_data = pickle.load(pkl_file)

            # Initialize spell checker
            logger.info('Initializing spell checker...')
            dictionary_path = _get_symspell_dictionary_path()
            spell_checker = SymSpell(max_dictionary_edit_distance=self.config.MAX_EDIT_DISTANCE)
            spell_checker.load_dictionary(dictionary_path, term_index=0, count_index=1)

            logger.info('All preprocessing components loaded successfully')
            return tokenizer, label_encoder, stopwords_set, corpus_data, spell_checker

        except Exception as e:
            logger.error(f'Failed to load preprocessing components: {str(e)}')
            raise

    def _load_preprocessing_components_from_azure_ml(self, components_dir: str) -> Tuple[Any, Any, set, list, SymSpell]:
        """
        Load all preprocessing components from Azure ML downloaded components directory.

        Loads tokenizer, label encoder, stopwords, corpus, and spell checker
        from the Azure ML downloaded components directory.

        Args:
            components_dir: Path to the directory containing downloaded components

        Returns:
            Tuple containing tokenizer, label encoder, stopwords, corpus, and spell checker

        Raises:
            Exception: If loading any component fails
        """
        try:
            components_path = Path(components_dir)
            logger.info(f'Loading preprocessing components from Azure ML downloads: {components_path}')

            # Load tokenizer
            logger.info('Loading tokenizer from Azure ML...')
            tk_path = components_path / 'tk.pkl'
            if not tk_path.exists():
                # Try alternative naming pattern
                tk_files = list(components_path.glob('*tk.pkl'))
                if tk_files:
                    tk_path = tk_files[0]
                else:
                    raise FileNotFoundError(f'Tokenizer file not found in {components_path}')

            with open(tk_path, 'rb') as pkl_file:
                tokenizer = pickle.load(pkl_file)
            logger.info(f'✅ Tokenizer loaded from: {tk_path}')

            # Load label encoder
            logger.info('Loading label encoder from Azure ML...')
            le_path = components_path / 'le.pkl'
            if not le_path.exists():
                # Try alternative naming pattern
                le_files = list(components_path.glob('*le.pkl'))
                if le_files:
                    le_path = le_files[0]
                else:
                    raise FileNotFoundError(f'Label encoder file not found in {components_path}')

            with open(le_path, 'rb') as pkl_file:
                label_encoder = pickle.load(pkl_file)
            logger.info(f'✅ Label encoder loaded from: {le_path}')

            # Load stopwords
            logger.info('Loading stopwords...')
            stopwords_set = set(stopwords.words('english'))

            # Load corpus
            logger.info('Loading corpus from Azure ML...')
            corpus_path = components_path / 'corpus.pkl'
            if not corpus_path.exists():
                # Try alternative naming pattern
                corpus_files = list(components_path.glob('*corpus.pkl'))
                if corpus_files:
                    corpus_path = corpus_files[0]
                else:
                    raise FileNotFoundError(f'Corpus file not found in {components_path}')

            with open(corpus_path, 'rb') as pkl_file:
                corpus_data = pickle.load(pkl_file)
            logger.info(f'✅ Corpus loaded from: {corpus_path}')

            # Initialize spell checker
            logger.info('Initializing spell checker...')
            dictionary_path = _get_symspell_dictionary_path()
            spell_checker = SymSpell(max_dictionary_edit_distance=self.config.MAX_EDIT_DISTANCE)
            spell_checker.load_dictionary(dictionary_path, term_index=0, count_index=1)

            # Update config to use Azure ML components path for abbreviations
            abbr_path = components_path / 'abbr.csv'
            if not abbr_path.exists():
                # Try alternative naming pattern
                abbr_files = list(components_path.glob('*abbr.csv'))
                if abbr_files:
                    abbr_path = abbr_files[0]
                else:
                    logger.warning(f'⚠️ Abbreviations file not found in {components_path}, using default')
                    abbr_path = None

            if abbr_path:
                # Update the config to point to the Azure ML downloaded abbreviations
                self.config.UTILS_PATH = str(components_path)
                logger.info(f'✅ Updated UTILS_PATH to Azure ML components: {self.config.UTILS_PATH}')

            logger.info('All preprocessing components loaded successfully from Azure ML')
            return tokenizer, label_encoder, stopwords_set, corpus_data, spell_checker

        except Exception as e:
            logger.error(f'Failed to load preprocessing components from Azure ML: {str(e)}')
            raise

    def initialize(self) -> None:
        """
        Initialize the scoring service with dual deployment architecture support.

        This method automatically detects the deployment mode:
        - Azure ML production mode: Downloads model from Azure ML Model Registry
        - Local development mode: Uses local model files from resources/ directory

        The mode is determined by environment variables and Azure environment detection.
        """
        try:
            logger.info('🚀 Starting model initialization with dual deployment architecture...')

            # Check if Azure ML mode is enabled and available
            if AZURE_ML_AVAILABLE and is_azure_ml_enabled():
                logger.info('🔵 Azure ML mode detected - initializing with Model Registry...')
                self._initialize_azure_ml_mode()
            else:
                logger.info(f'AZURE_ML_AVAILABLE: {AZURE_ML_AVAILABLE}, is_azure_ml_enabled(): {is_azure_ml_enabled()}')
                logger.info('🟡 Local development mode - initializing with local files...')
                self._initialize_local_mode()

            self._initialized = True
            logger.info('✅ Model initialization completed successfully')

        except Exception as e:
            logger.error(f'❌ Model initialization failed: {str(e)}')
            # If Azure ML mode fails, attempt fallback to local mode
            if AZURE_ML_AVAILABLE and is_azure_ml_enabled():
                logger.warning('⚠️ Azure ML mode failed, attempting fallback to local mode...')
                try:
                    self._initialize_local_mode()
                    self._initialized = True
                    logger.info('✅ Fallback to local mode successful')
                except Exception as fallback_error:
                    logger.error(f'❌ Fallback to local mode also failed: {str(fallback_error)}')
                    raise
            else:
                raise

    def _initialize_azure_ml_mode(self) -> None:
        """
        Initialize the scoring service using Azure ML Model Registry.

        Downloads fresh model and all processing components from Azure ML Model Registry
        with validation (no caching).
        """
        temp_dir_to_cleanup = None
        try:
            logger.info('📥 Downloading fresh model and components from Azure ML Model Registry...')

            # Download model and components from Azure ML Model Registry
            model_path, components_dir, model_version = get_azure_ml_model_and_components()

            # Store the temporary directory path for cleanup
            temp_dir_to_cleanup = (
                Path(components_dir).parent if Path(components_dir).parent.name.startswith('azureml_model_') else None
            )

            logger.info('✅ Model and components downloaded successfully:')
            logger.info(f'   Model Path: {model_path}')
            logger.info(f'   Components Dir: {components_dir}')
            logger.info(f'   Version: {model_version}')

            # Load the downloaded model
            logger.info('🔄 Loading downloaded model...')
            self._model = self._load_model_from_path(model_path)

            # Download NLTK data
            logger.info('📚 Downloading NLTK stopwords...')
            nltk.download('stopwords')

            # Load preprocessing components from Azure ML downloaded components
            logger.info('🔧 Loading preprocessing components from Azure ML downloads...')
            self._tokenizer, self._label_encoder, self._stopwords, self._corpus, self._spell_checker = (
                self._load_preprocessing_components_from_azure_ml(components_dir)
            )

            logger.info('🎉 Azure ML mode initialization completed successfully')

            # Clean up temporary directory after successful loading
            if temp_dir_to_cleanup and temp_dir_to_cleanup.exists():
                logger.info(f'🧹 Cleaning up temporary download directory: {temp_dir_to_cleanup}')
                import shutil

                shutil.rmtree(temp_dir_to_cleanup, ignore_errors=True)

        except Exception as e:
            logger.error(f'❌ Azure ML mode initialization failed: {str(e)}')

            # Clean up temporary directory on error
            if temp_dir_to_cleanup and temp_dir_to_cleanup.exists():
                logger.error(f'🧹 Cleaning up temporary directory due to error: {temp_dir_to_cleanup}')
                import shutil

                shutil.rmtree(temp_dir_to_cleanup, ignore_errors=True)

            raise

    def _initialize_local_mode(self) -> None:
        """
        Initialize the scoring service using local files from resources/ directory.

        Loads the Keras model and all preprocessing components (tokenizer, label encoder,
        corpus, and spell checker) from the self-contained resources directory.
        """
        try:
            logger.info('📁 Starting initialization with local files...')

            # Download NLTK data
            logger.info('📚 Downloading NLTK stopwords...')
            nltk.download('stopwords')

            # Load the Keras model from local path
            logger.info(f'🔄 Loading model from: {self.config.MODEL_PATH}')
            self._model = self._load_model_from_path(self.config.MODEL_PATH)

            # Load all preprocessing components
            logger.info('🔧 Loading preprocessing components...')
            self._tokenizer, self._label_encoder, self._stopwords, self._corpus, self._spell_checker = (
                self._load_preprocessing_components()
            )

            logger.info('🎉 Local mode initialization completed successfully')

        except Exception as e:
            logger.error(f'❌ Local mode initialization failed: {str(e)}')
            raise

    def initialize_local(self, model_path: Optional[str] = None) -> None:
        """
        Initialize for local testing mode - load model and components from local files.

        Uses the same local resources directory structure as the main initialize() method.
        All files are loaded from resources/ directory at repository root.

        Args:
            model_path: Optional custom model path. If None, uses config.MODEL_PATH
        """
        try:
            logger.info('Running in local testing mode...')

            # Use provided model path or default from config
            if model_path is None:
                model_path = self.config.MODEL_PATH

            logger.info(f'Loading model from: {model_path}')
            print(Path(model_path).resolve())

            # Load model directly
            self._model = keras.models.load_model(model_path)

            # Load preprocessing components
            with open(os.path.join(self.config.UTILS_PATH, 'tk.pkl'), 'rb') as pkl_file:
                self._tokenizer = pickle.load(pkl_file)

            with open(os.path.join(self.config.UTILS_PATH, 'le.pkl'), 'rb') as pkl_file:
                self._label_encoder = pickle.load(pkl_file)

            self._stopwords = set(stopwords.words('english'))

            with open(os.path.join(self.config.UTILS_PATH, 'corpus.pkl'), 'rb') as pkl_file:
                self._corpus = pickle.load(pkl_file)

            dictionary_path = _get_symspell_dictionary_path()
            self._spell_checker = SymSpell(max_dictionary_edit_distance=self.config.MAX_EDIT_DISTANCE)
            self._spell_checker.load_dictionary(dictionary_path, term_index=0, count_index=1)

            self._initialized = True
            logger.info('Local initialization completed successfully')

        except Exception as e:
            logger.error(f'Local initialization failed: {str(e)}')
            raise


def preprocess(input_data: str) -> Tuple[np.ndarray, List[Any]]:
    """
    Preprocess input data for model inference using optimized pipeline.

    Args:
        input_data: JSON string containing input data

    Returns:
        Tuple of processed features and pair IDs

    Raises:
        Exception: If preprocessing fails
    """
    try:
        # Get resource manager and components
        resource_manager = _get_resource_manager()
        if not resource_manager.is_initialized:
            raise RuntimeError('Resource manager not initialized. Call init() first.')

        # Initialize text preprocessor
        config = Config()
        text_processor = TextPreprocessor(config)

        # Parse input data
        logger.info('Starting preprocessing pipeline...')
        data = json.loads(input_data)
        treatments = [item['T'] for item in data]
        pair_ids = [item['Pair_ID'] for item in data]

        # Join treatments with separator for batch processing
        combined_text = ' linebreak '.join(treatments)

        # Process the combined text through the optimized pipeline
        processed_tokens = text_processor.process_text(
            text=combined_text,
            utils_path=resource_manager.config.UTILS_PATH,
            stopwords_set=resource_manager.stopwords,
            spell_checker=resource_manager.spell_checker,
            corpus=resource_manager.corpus,
        )

        # Split back into individual treatment tokens
        token_groups = ' '.join(processed_tokens).split('linebreak')

        # Tokenize using the loaded tokenizer
        if resource_manager.tokenizer is not None:
            feature_matrix = resource_manager.tokenizer.texts_to_matrix(token_groups, mode='binary')
        else:
            raise RuntimeError('Tokenizer not available')

        logger.info(f'Preprocessing completed for {len(pair_ids)} items')
        return feature_matrix, pair_ids

    except Exception as e:
        logger.error(f'Preprocessing failed: {str(e)}')
        raise


# Global instance for backward compatibility
_resource_manager: Optional[ModelResourceManager] = None


def _get_resource_manager() -> ModelResourceManager:
    """Get the global resource manager instance."""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = ModelResourceManager(Config())
    return _resource_manager


def _validate_required_fields(parsed_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Validate and log required fields in the input data.

    Args:
        parsed_data: List of dictionaries containing input data

    Returns:
        Dictionary containing validation results and field statistics
    """
    logger.debug('🔍 Validating required fields in input data...')

    required_fields = ['pair id', 't', 'amountexvat']
    field_variations = {
        'pair id': ['pair_id', 'pairid', 'pair id', 'id'],
        't': ['t', 'treatment', 'description'],
        'amountexvat': ['amountexvat', 'amount_ex_vat', 'amount', 'amountexcvat'],
    }

    validation_results = {
        'total_items': len(parsed_data),
        'field_presence': {},
        'missing_fields': [],
        'field_samples': {},
    }

    # Check each required field
    for field in required_fields:
        found_variations = []
        missing_count = 0
        sample_values = []

        for item in parsed_data:
            field_found = False
            # Check all possible variations (case-insensitive)
            for variation in field_variations[field]:
                for key in item.keys():
                    if key.lower() == variation.lower():
                        field_found = True
                        if variation not in found_variations:
                            found_variations.append(variation)
                        # Collect sample values (first 3 unique values)
                        if len(sample_values) < 3 and item[key] not in sample_values:
                            sample_values.append(item[key])
                        break
                if field_found:
                    break

            if not field_found:
                missing_count += 1

        validation_results['field_presence'][field] = {
            'found_variations': found_variations,
            'missing_count': missing_count,
            'present_count': len(parsed_data) - missing_count,
        }
        validation_results['field_samples'][field] = sample_values

        if missing_count > 0:
            validation_results['missing_fields'].append(field)

    # Log validation results
    logger.info('📝 Required field validation results:')
    for field in required_fields:
        field_info = validation_results['field_presence'][field]
        if field_info['missing_count'] == 0:
            logger.info(f'   ✅ {field}: Found in all {field_info["present_count"]} items')
            if field_info['found_variations']:
                logger.debug(f'      📋 Field variations found: {", ".join(field_info["found_variations"])}')
        else:
            logger.warning(
                f'   ⚠️ {field}: Missing in {field_info["missing_count"]} items, present in {field_info["present_count"]} items'
            )
            if field_info['found_variations']:
                logger.debug(f'      📋 Field variations found: {", ".join(field_info["found_variations"])}')

    # Log sample values for required fields
    logger.debug('📝 Sample values for required fields:')
    for field in required_fields:
        samples = validation_results['field_samples'][field]
        if samples:
            logger.debug(f'   • {field}: {samples}')
        else:
            logger.debug(f'   • {field}: No values found')

    return validation_results


def _log_input_metadata(parsed_data: List[Dict[str, Any]]) -> None:
    """
    Log metadata about the input data structure and statistics.

    Args:
        parsed_data: List of dictionaries containing input data
    """
    logger.info('📊 Input data metadata:')
    logger.info(f'   • Total items: {len(parsed_data)}')

    if not parsed_data:
        logger.warning('⚠️ No input data to analyze')
        return

    # Analyze field structure
    all_fields = set()
    field_counts = {}

    for item in parsed_data:
        for field in item.keys():
            all_fields.add(field)
            field_counts[field] = field_counts.get(field, 0) + 1

    logger.info(f'   • Unique fields found: {len(all_fields)}')
    logger.debug(f'   📋 All fields: {sorted(list(all_fields))}')

    # Log field presence statistics
    logger.debug('📊 Field presence statistics:')
    for field in sorted(all_fields):
        presence_rate = (field_counts[field] / len(parsed_data)) * 100
        logger.debug(f'   • {field}: {field_counts[field]}/{len(parsed_data)} items ({presence_rate:.1f}%)')

    # Analyze data types for first item
    if parsed_data:
        logger.debug('📊 Data types (based on first item):')
        for field, value in parsed_data[0].items():
            logger.debug(f'   • {field}: {type(value).__name__} = {repr(value)}')


def _log_input_sample(parsed_data: List[Dict[str, Any]], sample_size: int = 3) -> None:
    """
    Log a sample of the input data.

    Args:
        parsed_data: List of dictionaries containing input data
        sample_size: Number of items to include in the sample
    """
    if not parsed_data:
        logger.warning('⚠️ No input data to sample')
        return

    actual_sample_size = min(sample_size, len(parsed_data))
    logger.info(f'📝 Input data sample (first {actual_sample_size} items):')

    for i in range(actual_sample_size):
        logger.debug(f'   Item {i + 1}: {json.dumps(parsed_data[i], indent=2)}')


def _log_input_full(parsed_data: List[Dict[str, Any]]) -> None:
    """
    Log the complete input data.

    Args:
        parsed_data: List of dictionaries containing input data
    """
    if not parsed_data:
        logger.warning('⚠️ No input data to log')
        return

    logger.info(f'📝 Complete input data ({len(parsed_data)} items):')
    for i, item in enumerate(parsed_data):
        logger.debug(f'   Item {i + 1}: {json.dumps(item, indent=2)}')


def _log_comprehensive_input_data(parsed_data: List[Dict[str, Any]], log_level: str) -> Dict[str, Any]:
    """
    Log input data according to the specified logging level with comprehensive analysis.

    Args:
        parsed_data: List of dictionaries containing input data
        log_level: Logging level ('FULL', 'SAMPLE', 'METADATA_ONLY')

    Returns:
        Dictionary containing validation results from required field validation
    """
    logger.info('📝 Starting comprehensive input data logging...')
    logger.info(f'🔧 Input logging level: {log_level}')

    # Always validate required fields and log metadata
    validation_results = _validate_required_fields(parsed_data)
    _log_input_metadata(parsed_data)

    # Log data based on specified level
    if log_level == 'FULL':
        logger.info('📝 Logging complete input data (FULL mode)...')
        _log_input_full(parsed_data)
    elif log_level == 'SAMPLE':
        logger.info('📝 Logging input data sample (SAMPLE mode)...')
        _log_input_sample(parsed_data, sample_size=5)
    else:  # METADATA_ONLY
        logger.info('📝 Logging metadata only (METADATA_ONLY mode)...')
        # Metadata already logged above

    logger.debug('✅ Comprehensive input data logging completed')
    return validation_results


def init() -> None:
    """Initialize the scoring service using local files."""
    resource_manager = _get_resource_manager()
    resource_manager.initialize()


def run(data: str) -> Dict[str, Any]:
    """
    Run inference on the provided data with comprehensive error handling and logging.

    Args:
        data: JSON string containing input data

    Returns:
        Dictionary containing predictions and API version

    Raises:
        ValueError: If input data is invalid
        RuntimeError: If model components are not initialized
        Exception: If inference fails
    """
    # Record function start time for performance metrics
    function_start_time = time.time()

    try:
        # Function Entry Logging with Parameters
        logger.info('🚀 Starting ML inference pipeline...')
        logger.debug(f'📊 Input data length: {len(data) if data else 0} characters')

        # Log input data sample for debugging (first 200 chars)
        if data and len(data) > 0:
            sample_data = data[:200] + '...' if len(data) > 200 else data
            logger.debug(f'📝 Input data sample: {sample_data}')

        # Step 1: Input Validation
        validation_start_time = time.time()
        logger.debug('🔍 Step 1: Validating input data...')

        if not data:
            logger.error('❌ Input validation failed: Input data is empty')
            raise ValueError('Input data is empty')

        try:
            # Parse JSON to validate format
            parsed_data = json.loads(data)
            if not isinstance(parsed_data, list):
                logger.error('❌ Input validation failed: Data must be a JSON array')
                raise ValueError('Input data must be a JSON array')

            logger.info(f'✅ Input validation successful - {len(parsed_data)} items to process')
            logger.debug(f'⏱️ Input validation completed in {time.time() - validation_start_time:.3f}s')

        except json.JSONDecodeError as e:
            logger.error(f'❌ Input validation failed: Invalid JSON format - {str(e)}')
            raise ValueError(f'Invalid JSON format: {str(e)}')

        # Step 1.5: Comprehensive Input Data Logging
        input_logging_start_time = time.time()
        logger.debug('🔍 Step 1.5: Performing comprehensive input data logging...')

        try:
            # Load configuration to get logging level
            config = Config()

            # Perform comprehensive input data logging
            validation_results = _log_comprehensive_input_data(parsed_data, config.LOG_INPUT_LEVEL)

            # Log any critical validation issues
            if validation_results['missing_fields']:
                logger.warning(f'⚠️ Missing required fields detected: {", ".join(validation_results["missing_fields"])}')
                logger.info('💡 Processing will continue, but results may be affected')

            logger.info('✅ Comprehensive input data logging completed')
            logger.debug(f'⏱️ Input data logging completed in {time.time() - input_logging_start_time:.3f}s')

        except Exception as e:
            logger.warning(f'⚠️ Input data logging failed: {str(e)}')
            logger.info('💡 Continuing with processing despite logging failure')

        # Step 2: Resource Manager Initialization Check
        resource_check_start_time = time.time()
        logger.debug('🔍 Step 2: Checking resource manager initialization...')

        resource_manager = _get_resource_manager()
        if not resource_manager.is_initialized:
            logger.error('❌ Resource manager not initialized')
            raise RuntimeError('Model or label encoder not initialized. Call init() first.')

        logger.info('✅ Resource manager validation successful')
        logger.debug(f'⏱️ Resource validation completed in {time.time() - resource_check_start_time:.3f}s')

        # Step 3: Configuration Validation (config already loaded in Step 1.5)
        config_start_time = time.time()
        logger.debug('🔍 Step 3: Validating configuration...')

        # Config already loaded in Step 1.5, just log the active configuration
        logger.info('✅ Configuration validation successful')
        logger.info('🔧 Active configuration:')
        logger.info(f'   • Batch Size: {config.BATCH_SIZE}')
        logger.info(f'   • Top K Predictions: {config.TOP_K_PREDICTIONS}')
        logger.info(f'   • API Version: {config.API_VERSION}')
        logger.info(f'   • Max Edit Distance: {config.MAX_EDIT_DISTANCE}')
        logger.info(f'   • Min Word Length for Spell Check: {config.MIN_WORD_LENGTH_FOR_SPELL_CHECK}')
        logger.info(f'   • Input Logging Level: {config.LOG_INPUT_LEVEL}')
        logger.debug(f'⏱️ Configuration validation completed in {time.time() - config_start_time:.3f}s')

        # Step 4: Data Preprocessing
        preprocessing_start_time = time.time()
        logger.debug('🔍 Step 4: Starting data preprocessing pipeline...')

        feature_matrix, pair_ids = preprocess(data)
        is_empty = feature_matrix.sum(axis=1) == 0
        empty_count = np.sum(is_empty)

        logger.info('✅ Data preprocessing completed successfully')
        logger.info('📊 Preprocessing results:')
        logger.info(f'   • Total items processed: {len(pair_ids)}')
        logger.info(f'   • Empty treatments detected: {empty_count}')
        logger.info(f'   • Feature matrix shape: {feature_matrix.shape}')
        logger.debug(f'⏱️ Data preprocessing completed in {time.time() - preprocessing_start_time:.3f}s')

        # Step 5: Model Inference
        inference_start_time = time.time()
        logger.debug('🔍 Step 5: Running model inference...')
        logger.info(f'🤖 Starting model prediction with batch size: {config.BATCH_SIZE}')

        try:
            predictions = resource_manager.model.predict(feature_matrix, batch_size=config.BATCH_SIZE)
            logger.info('✅ Model inference completed successfully')
            logger.info(f'📊 Prediction results shape: {predictions.shape}')
            logger.debug(f'⏱️ Model inference completed in {time.time() - inference_start_time:.3f}s')

        except Exception as e:
            logger.error(f'❌ Model prediction failed: {str(e)}')
            logger.error(f'🔧 Model details: {type(resource_manager.model).__name__}')
            logger.error(f'🔧 Feature matrix shape: {feature_matrix.shape}')
            logger.error(f'🔧 Batch size: {config.BATCH_SIZE}')
            raise RuntimeError(f'Model prediction failed: {str(e)}')

        # Step 6: Top-K Prediction Processing
        topk_start_time = time.time()
        logger.debug('🔍 Step 6: Processing top-K predictions...')

        top_k_indices = predictions.argsort(axis=-1)[:, -config.TOP_K_PREDICTIONS :]
        top_k_probabilities = np.take_along_axis(arr=predictions, indices=top_k_indices, axis=-1)

        logger.info('✅ Top-K processing completed')
        logger.debug(f'📊 Top-K indices shape: {top_k_indices.shape}')
        logger.debug(f'📊 Top-K probabilities shape: {top_k_probabilities.shape}')
        logger.debug(f'⏱️ Top-K processing completed in {time.time() - topk_start_time:.3f}s')

        # Step 7: Label Transformation
        label_transform_start_time = time.time()
        logger.debug('🔍 Step 7: Transforming predictions to labels...')

        try:
            pred_1 = resource_manager.label_encoder.inverse_transform(top_k_indices[:, -1])
            pred_2 = resource_manager.label_encoder.inverse_transform(top_k_indices[:, -2])
            pred_3 = resource_manager.label_encoder.inverse_transform(top_k_indices[:, -3])
            pred_4 = resource_manager.label_encoder.inverse_transform(top_k_indices[:, -4])
            pred_5 = resource_manager.label_encoder.inverse_transform(top_k_indices[:, -5])

            logger.info('✅ Label transformation completed successfully')
            logger.debug(f'⏱️ Label transformation completed in {time.time() - label_transform_start_time:.3f}s')

        except Exception as e:
            logger.error(f'❌ Label transformation failed: {str(e)}')
            logger.error(f'🔧 Label encoder classes: {len(resource_manager.label_encoder.classes_)}')
            logger.error(f'🔧 Top-K indices range: [{top_k_indices.min()}, {top_k_indices.max()}]')
            raise RuntimeError(f'Label transformation failed: {str(e)}')

        # Step 8: Apply T202 Rule for Empty Treatments
        t202_start_time = time.time()
        logger.debug('🔍 Step 8: Applying T202 rule for empty treatments...')

        if empty_count > 0:
            logger.info(f'🔧 Applying T202 rule to {empty_count} empty treatments')
            pred_1[is_empty] = ['999']
            pred_2[is_empty] = ['NULL']
            pred_3[is_empty] = ['NULL']
            pred_4[is_empty] = ['NULL']
            pred_5[is_empty] = ['NULL']

            top_k_probabilities[is_empty, -1] = 1
            top_k_probabilities[is_empty, -2] = 0
            top_k_probabilities[is_empty, -3] = 0
            top_k_probabilities[is_empty, -4] = 0
            top_k_probabilities[is_empty, -5] = 0

            logger.info('✅ T202 rule applied successfully')
        else:
            logger.debug('ℹ️ No empty treatments found, T202 rule not needed')

        logger.debug(f'⏱️ T202 rule processing completed in {time.time() - t202_start_time:.3f}s')

        # Step 9: Format Final Results
        formatting_start_time = time.time()
        logger.debug('🔍 Step 9: Formatting prediction results...')

        prediction_results = []
        for i in range(len(pair_ids)):
            prediction_dict = {
                'Pair_ID': pair_ids[i],
                'Predict1': pred_1[i],
                'Predict1_confidence': f'{top_k_probabilities[i, -1]:.2f}',
                'Predict2': pred_2[i],
                'Predict2_confidence': f'{top_k_probabilities[i, -2]:.2f}',
                'Predict3': pred_3[i],
                'Predict3_confidence': f'{top_k_probabilities[i, -3]:.2f}',
                'Predict4': pred_4[i],
                'Predict4_confidence': f'{top_k_probabilities[i, -4]:.2f}',
                'Predict5': pred_5[i],
                'Predict5_confidence': f'{top_k_probabilities[i, -5]:.2f}',
            }
            prediction_results.append(prediction_dict)

        result_dict = {'Predictions': prediction_results, 'API_Version_No': config.API_VERSION}

        logger.info('✅ Result formatting completed successfully')
        logger.debug(f'⏱️ Result formatting completed in {time.time() - formatting_start_time:.3f}s')

        # Function Exit Logging with Performance Metrics
        total_execution_time = time.time() - function_start_time
        logger.info('🎉 ML inference pipeline completed successfully!')
        logger.info('📊 Final results summary:')
        logger.info(f'   • Total items processed: {len(prediction_results)}')
        logger.info(f'   • Empty treatments handled: {empty_count}')
        logger.info(f'   • API Version: {config.API_VERSION}')
        logger.info(f'⏱️ Total execution time: {total_execution_time:.3f}s')
        logger.info(f'⚡ Average time per item: {total_execution_time / len(prediction_results):.4f}s')

        return result_dict

    except Exception as e:
        total_execution_time = time.time() - function_start_time
        logger.error(f'❌ ML inference pipeline failed after {total_execution_time:.3f}s')
        logger.error(f'💥 Error details: {str(e)}')
        logger.error(f'🔧 Error type: {type(e).__name__}')
        raise


if __name__ == '__main__':
    # Local testing mode - use resource manager for initialization
    try:
        logger.info('Running in local testing mode...')

        # Initialize using the new local file approach
        init()

        # Test inference
        test_data = '[{"Pair_ID": 1001, "T": "Small Animal Consultation", "AmountExVat": 80.91}, {"Pair_ID": 1002, "T": "Cytology(SVC)", "AmountExVat": 63.64}, {"Pair_ID": 1003, "T": "Discount", "AmountExVat": -69}, {"Pair_ID": 1004, "T": "Discount For Multiple Blood Tests", "AmountExVat": -40.32}, {"Pair_ID": 1005, "T": "In House - IDEXX Catalyst Chem 10 Clip", "AmountExVat": 128.68}, {"Pair_ID": 1006, "T": "Discount Given", "AmountExVat": -81.44}]'
        result = run(test_data)
    except Exception as e:
        logger.error(f'Local testing failed: {str(e)}')
        raise

"""
Azure ML Model Registry client for downloading trained models.

This module provides functionality to download models from Azure ML Model Registry
with comprehensive logging, MD5 validation, and caching capabilities. It integrates
with the existing dual deployment architecture to support both Azure ML production
mode and local development mode.
"""

import hashlib
import os
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import List, Optional, Tuple

from azure.ai.ml import MLClient
from azure.core.exceptions import ResourceNotFoundError
from azure.identity import EnvironmentCredential
from dotenv import load_dotenv
from loguru import logger

# Load environment variables
load_dotenv()


@dataclass
class AzureMLConfig:
    """Configuration for Azure ML Model Registry integration."""

    # Azure ML workspace connection details
    subscription_id: str
    resource_group: str
    workspace_name: str

    # Model details
    model_name: str
    model_version: Optional[str] = None  # None means latest version

    # MD5 validation
    expected_md5_hash: Optional[str] = None

    # Processing components configuration
    required_components: Optional[List[str]] = None

    def __post_init__(self):
        """Initialize default required components if not provided."""
        if self.required_components is None:
            self.required_components = ['abbr.csv', 'corpus.pkl', 'le.pkl', 'tk.pkl']

    @classmethod
    def from_environment(cls) -> 'AzureMLConfig':
        """
        Create AzureMLConfig from environment variables.

        Returns:
            AzureMLConfig: Configuration loaded from environment variables

        Raises:
            ValueError: If required environment variables are missing
        """
        # Load required Azure ML configuration
        subscription_id = os.getenv('AZURE_ML_SUBSCRIPTION_ID')
        resource_group = os.getenv('AZURE_ML_RESOURCE_GROUP')
        workspace_name = os.getenv('AZURE_ML_WORKSPACE_NAME')
        model_name = os.getenv('AZURE_ML_MODEL_NAME')

        # Validate required variables
        missing_vars = []
        if not subscription_id:
            missing_vars.append('AZURE_ML_SUBSCRIPTION_ID')
        if not resource_group:
            missing_vars.append('AZURE_ML_RESOURCE_GROUP')
        if not workspace_name:
            missing_vars.append('AZURE_ML_WORKSPACE_NAME')
        if not model_name:
            missing_vars.append('AZURE_ML_MODEL_NAME')

        if missing_vars:
            raise ValueError(f'Missing required environment variables: {", ".join(missing_vars)}')

        # Log loaded configuration
        logger.info('🔧 Loaded Azure ML configuration from environment variables:')
        logger.info(f'   Subscription ID: {subscription_id[:8] if subscription_id else "None"}...')
        logger.info(f'   Resource Group: {resource_group}')
        logger.info(f'   Workspace Name: {workspace_name}')
        logger.info(f'   Model Name: {model_name}')

        # Load optional configuration
        model_version = os.getenv('AZURE_ML_MODEL_VERSION')
        expected_md5_hash = os.getenv('AZURE_ML_MODEL_MD5_HASH')

        if model_version:
            logger.info(f'   Model Version: {model_version}')
        else:
            logger.info('   Model Version: latest (not specified)')

        if expected_md5_hash:
            logger.info(f'   Expected MD5 Hash: {expected_md5_hash}')
        else:
            logger.warning(
                '⚠️ MD5 hash validation not configured - set AZURE_ML_MODEL_MD5_HASH for integrity validation'
            )

        return cls(
            subscription_id=subscription_id,  # type: ignore
            resource_group=resource_group,  # type: ignore
            workspace_name=workspace_name,  # type: ignore
            model_name=model_name,  # type: ignore
            model_version=model_version,
            expected_md5_hash=expected_md5_hash,
        )


class AzureMLModelDownloader:
    """
    Downloads and manages models from Azure ML Model Registry.

    This class handles fresh model downloading with MD5 validation
    and comprehensive error handling. It integrates with the existing
    logging infrastructure using Loguru with emoji conventions.

    Note: This implementation always downloads fresh copies without caching
    to ensure the latest model and components are always used.
    """

    def __init__(self, config: AzureMLConfig):
        """
        Initialize the Azure ML model downloader.

        Args:
            config: Azure ML configuration
        """
        self.config = config
        self._ml_client: Optional[MLClient] = None

    @property
    def ml_client(self) -> MLClient:
        """Get or create the Azure ML client with lazy initialization."""
        if self._ml_client is None:
            logger.info('🔐 Initializing Azure ML client with EnvironmentCredential...')
            try:
                credential = EnvironmentCredential()
                self._ml_client = MLClient(
                    credential=credential,
                    subscription_id=self.config.subscription_id,
                    resource_group_name=self.config.resource_group,
                    workspace_name=self.config.workspace_name,
                )
                logger.info('✅ Azure ML client initialized successfully')
            except Exception as e:
                logger.error(f'❌ Failed to initialize Azure ML client: {str(e)}')
                raise

        return self._ml_client

    def _calculate_file_md5(self, file_path: Path) -> str:
        """
        Calculate MD5 hash of a file in chunks for memory efficiency.

        Args:
            file_path: Path to the file to calculate MD5 hash for

        Returns:
            MD5 hash as lowercase hexadecimal string

        Raises:
            Exception: If file I/O operations fail
        """
        try:
            md5_hash = hashlib.md5()

            with open(file_path, 'rb') as f:
                # Read file in chunks to handle large files efficiently
                for chunk in iter(lambda: f.read(8192), b''):
                    md5_hash.update(chunk)

            return md5_hash.hexdigest().lower()

        except Exception as e:
            logger.error(f'❌ Failed to calculate MD5 hash for file {file_path}: {str(e)}')
            raise

    def _validate_model_md5(self, file_path: Path, expected_hash: str) -> bool:
        """
        Validate model file MD5 hash.

        Args:
            file_path: Path to the model file
            expected_hash: Expected MD5 hash

        Returns:
            True if validation passes, False otherwise
        """
        logger.info('🔍 Performing MD5 validation for downloaded model...')

        calculated_hash = self._calculate_file_md5(file_path)
        logger.info(f'📋 Calculated MD5 hash: {calculated_hash}')
        logger.info(f'📋 Expected MD5 hash: {expected_hash}')

        if calculated_hash.lower() == expected_hash.lower():
            logger.info('✅ MD5 validation passed - model integrity verified')
            return True
        else:
            logger.error('❌ MD5 validation failed - model integrity compromised')
            logger.error(f'   Expected: {expected_hash}')
            logger.error(f'   Actual: {calculated_hash}')
            return False

    def download_model_and_components(self) -> Tuple[str, str, str]:
        """
        Download model and all processing components from Azure ML Model Registry with validation.

        This method implements the complete model and components download workflow:
        1. Download fresh model and components from Azure ML Model Registry
        2. Validate model integrity with MD5 hash (if configured)
        3. Return the paths to the model file, components directory, and version

        Returns:
            Tuple[str, str, str]: (model_file_path, components_dir_path, model_version)

        Raises:
            Exception: If model or components download/validation fails
        """
        try:
            logger.info('🚀 Starting Azure ML model and components download process...')
            logger.info(f'   Model Name: {self.config.model_name}')
            logger.info(f'   Requested Version: {self.config.model_version or "latest"}')
            logger.info('   Mode: Fresh download (no caching)')

            # Get model from Azure ML Model Registry
            logger.info('🔍 Retrieving model information from Azure ML Model Registry...')

            try:
                if self.config.model_version:
                    model = self.ml_client.models.get(name=self.config.model_name, version=self.config.model_version)
                else:
                    # Get latest version
                    model = self.ml_client.models.get(name=self.config.model_name, label='latest')

                logger.info('✅ Model found in registry:')
                logger.info(f'   Name: {model.name}')
                logger.info(f'   Version: {model.version}')
                logger.info(f'   Description: {model.description or "No description"}')

            except ResourceNotFoundError:
                error_msg = f'Model {self.config.model_name} not found in Azure ML Model Registry'
                if self.config.model_version:
                    error_msg += f' (version: {self.config.model_version})'
                logger.error(f'❌ {error_msg}')
                raise Exception(error_msg)

            # Always download fresh from Azure ML
            model_version = str(model.version)
            logger.info('⬇️ Downloading fresh model and components from Azure ML Model Registry...')

            # Create persistent temporary directory for download
            import shutil
            import tempfile

            temp_dir = tempfile.mkdtemp(prefix=f'azureml_model_{self.config.model_name}_v{model_version}_')
            temp_path = Path(temp_dir)

            try:
                # Download model package to temporary location
                logger.info(f'📁 Downloading to temporary location: {temp_path}')
                self.ml_client.models.download(
                    name=model.name, version=str(model.version), download_path=str(temp_path)
                )

                logger.info(f'✅ Model package downloaded successfully to: {temp_path}')

                # Find the model file and components in the downloaded directory
                downloaded_model_dir = temp_path / model.name
                if not downloaded_model_dir.exists():
                    # Try alternative directory structure
                    downloaded_model_dir = temp_path

                model_file_path = self._find_model_file(downloaded_model_dir)
                if not model_file_path:
                    raise Exception(f'No model file found in downloaded directory: {downloaded_model_dir}')

                logger.info(f'📄 Model file found: {model_file_path}')

                # Validate model file if MD5 hash is configured
                if self.config.expected_md5_hash:
                    if not self._validate_model_md5(model_file_path, self.config.expected_md5_hash):
                        raise Exception('Model validation failed - MD5 hash mismatch')

                # Find and log processing components
                component_paths = {}
                if self.config.required_components:
                    for component_name in self.config.required_components:
                        component_path = downloaded_model_dir / component_name
                        if component_path.exists():
                            component_paths[component_name] = component_path
                            logger.info(f'📄 Component found: {component_name}')
                        else:
                            logger.warning(f'⚠️ Component not found in package: {component_name}')

                # Log final details
                model_size = model_file_path.stat().st_size
                logger.info('🎉 Model and components download completed successfully:')
                logger.info(f'   Model Path: {model_file_path}')
                logger.info(f'   Components Dir: {downloaded_model_dir}')
                logger.info(f'   Model Version: {model_version}')
                logger.info(f'   File Size: {model_size / (1024 * 1024):.2f} MB')
                required_count = len(self.config.required_components) if self.config.required_components else 0
                logger.info(f'   Components found: {len(component_paths)}/{required_count}')
                logger.info(f'   Temp Directory: {temp_path} (will be cleaned up by caller)')

                # Return paths to the downloaded files
                return str(model_file_path), str(downloaded_model_dir), model_version

            except Exception:
                # Clean up temporary directory on error
                logger.error(f'❌ Cleaning up temporary directory due to error: {temp_path}')
                shutil.rmtree(temp_path, ignore_errors=True)
                raise

        except Exception as e:
            logger.error(f'❌ Model and components download failed: {str(e)}')
            raise

    def _find_model_file(self, download_path: Path) -> Optional[Path]:
        """
        Find the model file in the downloaded directory.

        Args:
            download_path: Path to the downloaded model directory

        Returns:
            Path to the model file, or None if not found
        """
        # Common model file extensions
        model_extensions = ['.h5', '.keras', '.pb', '.pkl']

        # Search for model files
        for ext in model_extensions:
            model_files = list(download_path.rglob(f'*{ext}'))
            if model_files:
                # Return the first model file found
                return model_files[0]

        # If no specific model files found, check if the directory itself is a SavedModel
        if (download_path / 'saved_model.pb').exists():
            return download_path

        return None


def is_azure_ml_enabled() -> bool:
    """
    Check if Azure ML mode is enabled via environment variables.

    Returns:
        True if Azure ML mode is enabled, False for local mode
    """
    # Check if Azure ML is explicitly enabled
    azure_ml_enabled = os.getenv('AZURE_ML_ENABLE', '').lower() in ('true', '1', 'yes', 'on')

    # Auto-detect Azure environment (running on Azure ML compute)
    is_azure_environment = (
        os.getenv('AZUREML_RUN_ID') is not None
        or os.getenv('AZUREML_ARM_SUBSCRIPTION') is not None
        or os.getenv('AML_PARAMETER_azureml_run_id') is not None
    )

    enabled = azure_ml_enabled or is_azure_environment

    if enabled:
        logger.info('🔵 Azure ML mode enabled - will download model from Model Registry')
    else:
        logger.info('🟡 Local development mode - will use local model files')

    return enabled


def get_azure_ml_model_path() -> Tuple[str, str]:
    """
    Get model path using Azure ML Model Registry integration.

    This function handles the complete Azure ML model download workflow
    with proper error handling and fallback mechanisms.

    Returns:
        Tuple[str, str]: (model_file_path, model_version)

    Raises:
        Exception: If Azure ML configuration is invalid or download fails
    """
    try:
        # Load Azure ML configuration from environment
        config = AzureMLConfig.from_environment()

        # Create downloader and download model and components
        downloader = AzureMLModelDownloader(config)
        model_path, components_dir, model_version = downloader.download_model_and_components()

        # Return model path and version for backward compatibility
        return model_path, model_version

    except Exception as e:
        logger.error(f'❌ Azure ML model download failed: {str(e)}')
        logger.error('💡 Falling back to local model files...')
        raise


def get_azure_ml_model_and_components() -> Tuple[str, str, str]:
    """
    Get model and components paths using Azure ML Model Registry integration.

    This function handles the complete Azure ML model and components download workflow
    with proper error handling and fallback mechanisms.

    Returns:
        Tuple[str, str, str]: (model_file_path, components_dir_path, model_version)

    Raises:
        Exception: If Azure ML configuration is invalid or download fails
    """
    try:
        # Load Azure ML configuration from environment
        config = AzureMLConfig.from_environment()

        # Create downloader and download model and components
        downloader = AzureMLModelDownloader(config)
        return downloader.download_model_and_components()

    except Exception as e:
        logger.error(f'❌ Azure ML model and components download failed: {str(e)}')
        logger.error('💡 Falling back to local model files...')
        raise
